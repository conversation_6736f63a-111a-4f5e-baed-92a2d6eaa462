Imports Newtonsoft.Json.Linq
Imports Newtonsoft.Json
Imports System.Net
Imports VI.DB.Entities
Imports VI.DB.Specialized

Public Function CCC_RevvoLearning_Auth(ByVal UID_TSBAccountDef As String) As String
    Dim accessToken As String = Nothing ' Variável para armazenar o token de acesso

    Try
        ' Obter endpoints baseados no AccountDef
        Dim endpoints As Dictionary(Of String, String) = CCC_RevvoLearning_GetEndpoints(UID_TSBAccountDef)
        
        If endpoints.Count = 0 Then
            Console.WriteLine("Erro: Nenhum endpoint configurado para o AccountDef fornecido")
            Return Nothing
        End If
        
        Dim clientId As String = endpoints("ClientId")
        Dim clientSecret As String = endpoints("ClientSecret")
        Dim tokenURL As String = endpoints("TokenURL")

        Console.WriteLine("Usando ambiente: " & endpoints("Environment") & " - TokenURL: " & tokenURL)

        Using client As New WebClient()
            ' Define o Content-Type para indicar que o corpo da requisição é JSON
            client.Headers(HttpRequestHeader.ContentType) = "application/json"

            ' Prepara os dados JSON para a requisição de autenticação
            ' É crucial que a string JSON seja formatada corretamente com aspas duplas escapadas.
            Dim jsonData As String = String.Format(
                "{{""grant_type"": ""client_credentials"", ""client_id"": ""{0}"", ""client_secret"": ""{1}""}}",
                clientId, clientSecret
            )

            ' Converte a string JSON para um array de bytes usando UTF8, que é padrão para JSON
            Dim requestBytes As Byte() = System.Text.Encoding.UTF8.GetBytes(jsonData)

            ' Realiza a requisição POST para o endpoint de token
            Dim responseBytes As Byte() = client.UploadData(tokenURL, "POST", requestBytes)

            ' Converte a resposta em bytes para uma string usando UTF8
            Dim responseString As String = System.Text.Encoding.UTF8.GetString(responseBytes)

            ' Desserializa a string JSON da resposta para um objeto JObject
            Dim jsonResponse As JObject = JObject.Parse(responseString)

            ' Verifica se o objeto JSON e o campo 'access_token' existem antes de tentar acessá-los
            If jsonResponse IsNot Nothing AndAlso jsonResponse("access_token") IsNot Nothing Then
                accessToken = jsonResponse("access_token").ToString()

                Console.WriteLine("Token de acesso obtido com sucesso.")
            Else
                Console.WriteLine("Resposta JSON não contém 'access_token' ou está vazia.")
                Console.WriteLine("Resposta completa: " & responseString)
            End If

        End Using ' Garante que o WebClient seja descartado corretamente

    Catch ex As WebException
        ' Captura exceções específicas de requisições web (e.g., erros HTTP 4xx, 5xx)
        Console.WriteLine("Erro na requisição web durante a autenticação: " & ex.Message)
        If ex.Response IsNot Nothing Then
            Using reader As New System.IO.StreamReader(ex.Response.GetResponseStream())
                Dim errorResponse As String = reader.ReadToEnd()
                Console.WriteLine("Resposta de erro do servidor: " & errorResponse)
            End Using
        End If
        ' Em caso de erro, retorna Nothing para indicar falha na obtenção do token
        Return Nothing
    Catch ex As Exception
        ' Captura outras exceções genéricas
        Console.WriteLine("Ocorreu um erro inesperado durante a autenticação: " & ex.Message)
        ' Em caso de erro, retorna Nothing
        Return Nothing
    End Try

    Return accessToken ' Retorna o token de acesso (ou Nothing se houve erro)
End Function

' Função auxiliar para determinar URLs baseadas no AccountDef
Public Function CCC_RevvoLearning_GetEndpoints(ByVal UID_TSBAccountDef As String) As Dictionary(Of String, String)
    Dim endpoints As New Dictionary(Of String, String)

    Try
        Dim AccountDef As String = UID_TSBAccountDef
        
        ' Definir mapeamento de ambientes
        Dim environments() As String = {"DASA", "AmericasFunc", "AmericasMed"}
        
        ' Buscar o ambiente correspondente
        For Each env As String In environments
            Dim configAccountDef As String = Connection.GetConfigParm($"Custom\CustomTargetSystem\RevvoLearning\{env}\UIDAccountDef{env}")
            
            If AccountDef = configAccountDef Then
                endpoints("TokenURL") = Connection.GetConfigParm($"Custom\CustomTargetSystem\RevvoLearning\{env}\TokenURL")
                endpoints("URL") = Connection.GetConfigParm($"Custom\CustomTargetSystem\RevvoLearning\{env}\URL")
                endpoints("ClientId") = Connection.GetConfigParm($"Custom\CustomTargetSystem\RevvoLearning\{env}\ClientId")
                endpoints("ClientSecret") = Connection.GetConfigParm($"Custom\CustomTargetSystem\RevvoLearning\{env}\ClientSecret")
                endpoints("Environment") = env
                
                Console.WriteLine($"Ambiente encontrado: {env}")
                Exit For
            End If
        Next
        
        ' Verificar se encontrou algum ambiente
        If endpoints.Count = 0 Then
            Console.WriteLine($"AVISO: AccountDef '{AccountDef}' não encontrado nos ambientes configurados")
            Console.WriteLine("Ambientes disponíveis:")
            For Each env As String In environments
                Dim availableAccountDef As String = Connection.GetConfigParm($"Custom\CustomTargetSystem\RevvoLearning\{env}\UIDAccountDef{env}")
                Console.WriteLine($"  - {env}: {availableAccountDef}")
            Next
        End If

    Catch ex As Exception
        Console.WriteLine("Erro ao determinar endpoints: " & ex.Message)
    End Try

    Return endpoints
End Function

